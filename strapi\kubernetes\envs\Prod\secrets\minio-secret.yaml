apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  annotations:
    sealedsecrets.bitnami.com/namespace-wide: 'true'
  name: ${BUILD_REPOSITORY_NAME}-minio-secret
  namespace: ${NAMESPACE}
  labels:
    app: ${BUILD_REPOSITORY_NAME}
    source: ${BUILD_REPOSITORY_NAME}
spec:
  encryptedData:
    MINIO_ACCESS_KEY: AgBxFThcsOQmlNvPX2jkiBGDOsL0gzg+eS05cdV5COiDHOBVOr4e7CUxR8SAzaQTZkz5q3C/2rgkfgQ9CGIAdKjzpr/iK8SEtuaY/t5Oxl65JkSZOINjNjieZ1o+oj60538ld1p6VF6sMWF0UAQ3cpki3iUMjD6fpVA3C7RAGTGeRBADJNEvPDhOMIwTgbwS09773yEhXn6JDPCPvDYpnhusStKnZHDI1Ev9qxJtInmhT+21tOkkAjHEWGECer2F0zvcvptbrGvOQDsnbFWhwVV6RUNQ1EJE0tepKx9PA14la9fV8WA4sGqNjArAxZolrrPYtx9+Aszie28K7dWvdrws+J1yYVLn2rjkIAcu/KM4/uFafMO8sXb8qpoMwE2Aca0ETQOAHHLCFunvmeHOPc2SgNTE7GjXbFFqQQFPetWLvWhOkOg40HPwVuKglzwol+2PIsQPflESjsxB2nP7tEPBTPDst8SAtLWEbJqCpNp+hvTjLxomkpUfkVrGfM6sYiAObd6bzdUdtQVw0oDn3oKdHHSIlcr9fdXdtQgnMDquKK1/TLwfCzQViQgM+QCDnprMNS0bQ5W1wngJ990UahoaIx0KKosmkBWLb1lSA1hOputPOSpYZBK+Oz/gEFSbRATCEO18y/aD1+wdus451zfhCMN8EY7Lfgo5AiEGYpmyFojwG7zJTbn7caJ6a81bazgC+rASWGPxz1DzjhA=
    MINIO_SECRET_KEY: AgDHkOZaPVGe1RRKROJuCqXqHRoppBFNbC50LGMKDHK4Aur0Fyr5Prz6s2Er5NAbgeTpmLWEF2p7IeTtwX8ocOl2A2B9oGraxY3y/Y41vScNBUTZr1KpwttgS728/dzPy27gjqzAUHFoVDVhUDsfpF0o3+Uz4tOcPelii3FwP1CUDRGWpQ1gu2b5ThZ10N+xSV7fWhoyPOSuX5aJFjzpz2XX15oFscsGYBuJ6z1ZTYosBpd5+CQ387eq07yP0zjENw7I2G2xpIvJQv04RW4SGgVfmAyZN7wLW8p5lDAr+WzRGQOAZ7WLGNcH+EB3uQ4Mkd5NAk9b1vMvaJnjWawJ8dGxUOp1iQQ0uUEq4F17DYDKZMn7CO3zJmSl1mesxL6EfkE83aidkJFrWyaTv/G0ILTG4xQ8PjderB4s198KJbsGYs5bVGWPH8iDoX5p8guc3fPUUKszcK9+03UuDsJi4XB0Sc7kXjAhLrpdfSbWZfcgFtin+sTufuX0AS0cLpGfCIuLZYQDP5HfF7xoW2kmEY0XDMK5siv4S0hzV+gIE1CtIbNWu/69LZ1jHyro1PDZ5WX55oIbudhKmiDmcmEsOGM5vzDijSWEI1BW7xdoCGsHly3UUVrJNQIex2HLms532/0HNqRjUsowv526IAK2m92j6Bs05YgG7hr5qg0E2gFiC7kPznedxe1rViNQSy40ICymKy6R2wfQmZV/AIriUF2W6RrE7YilcB2HXUAojhh/Z5uYE0DX8+TWuOR3NZxQK1tanzTLDc0REw==
  template:
    metadata:
      annotations:
        sealedsecrets.bitnami.com/namespace-wide: 'true'
      labels:
        app: ${BUILD_REPOSITORY_NAME}
        source: ${BUILD_REPOSITORY_NAME}
      name: ${BUILD_REPOSITORY_NAME}-minio-secret
      namespace: ${NAMESPACE}
