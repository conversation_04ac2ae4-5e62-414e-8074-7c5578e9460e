apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  annotations:
    sealedsecrets.bitnami.com/namespace-wide: 'true'
  name: ${BUILD_REPOSITORY_NAME}-minio-secret
  namespace: ${NAMESPACE}
  labels:
    app: ${BUILD_REPOSITORY_NAME}
    source: ${BUILD_REPOSITORY_NAME}
spec:
  encryptedData:
    MINIO_ACCESS_KEY: AgADFugetARckdYr0j3v5XuwoOb/VYghSssG8wj29xGazcUuBIOFeDMsdKKmagJKVk7NgTNft8qp1hP+pvcTUpSGmPHlzC9omxDqWCrRNB0jZH0G5zMbZcw7loEB0p3sMF8fQ8zfBSzfBVR9El5XnXezv+us2Bc7ho+3YuqDPAQbabd1cfpExW8wZErI/FwhKizOLFNliYSYM+4uiTXtBvzrwpqxEqF3DJ/Y1gdbjbdJ5hN277F3PNyyPYTe4xj7ytDenCcYfovcG0sL49FnjrtK4DcLJg5sOW/T847uPLjG3e7sVrYLDTkw2lJVdrtxXbdR7hGLR9NSIcNvdhKwDHdNDq0IEO5BmaScrpm7FekasgVhc5prmArvdAzQKOamB+A8ArWPhWjDbV3hxjus0XKsk7lgAg71Gxh12ATJ1F5lq/YsURzOv5R/rC+CAu633M/t3cC9iL4DktInHB6EmKweNyFt/PInAiD2V8KN3onwh2WauTdrZH5qRa0FpB++Wwjtn8/4pQhrpV6D9Gyk20/3b2vQdJE5p0sPOH9NRmkaK2vHSkvkgSx6u+3HTPrAW7OkqYY9QmV1vkoTecpQTaXhAOtypnb5wTH803Tz+nSotxpmAe089Kwdyl22TKpSQ6FuYGwzNWZRQOujLgHo2gBAe5t/4h4rZTKwu5Ero/Dq0F0EoJQ5rWS4mM/6JAKYDWMoSHrxnp7h+MMIL2o=
    MINIO_SECRET_KEY: AgCFWzVgdiFfQ+J/Om44PnZ6JjE5creVo8UzYvhKW/TCbH07AGcgiu0U5ZYlW46E+V9MsMNXyLaQJBMSrSDB2iYHuek9nrIF7ZOk+z7Xm9dqD4EXH038xE4rmLnZbEMVfGqTWRY3W+eZ5RQn6pdasky1GEWyDUxN6SfXJny9oNzOQ0F6tTH8xaBXqP08vay7I+qXkdVo8UlRJb8N5FDlY7t+nNXNdHKhkhajIGnVpql6dkhR03i+C3eSpiTAmHirxg/kwx4e7MCOql8WtNJkLf+G9d3MWv+NyAmNRMZHl7ApnMYStaPSq4AjybY8P3tTtgSFqI7QME+rPMe3v+/D9ZUsbFIY7B2x9Duf3tvWUYOMi6mu81DGQdZRf5gXCsCHmc85AHUxB0V9zu3/GBgQj1PyrGVKcrZCxJ3WZSrh4Rq09gI7PJvk/u5tke4QPnkFmqB64aA1QP+m1pHM49kaDWoL8HbQlzSPryBnGba/AWBJcAfixj+R87cIDvKNfvMwg0YCHrQGmxE1XRrnPXLdDOTcFFOK5T3qfBnIF4Wc/1cszozb2dZtEn+J9k/XTLS2olsKBHi0w6OsfoiSbZEr3FdeCVjAOjegMVpgefobSIZttEjOX5zcpOELtA+qizMje/XD8HpNt8f9kPlAfHMncljq+TBRik+vRIkbJROPZN8mG/K/pGxzlmpI/LvmZcEc9i4todLpiyII8tWPCdA7i1rsESxApT9IVtuLvSAXl4cLm+2bkx1ZHtJvbl5w8vu2Qe3vimbp4ke28A==
  template:
    metadata:
      annotations:
        sealedsecrets.bitnami.com/namespace-wide: 'true'
      labels:
        app: ${BUILD_REPOSITORY_NAME}
        source: ${BUILD_REPOSITORY_NAME}
      name: ${BUILD_REPOSITORY_NAME}-minio-secret
      namespace: ${NAMESPACE}
