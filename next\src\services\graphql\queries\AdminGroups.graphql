fragment AdminGroupDocumentIdEntity on AdminGroup {
  documentId
}

fragment PageSubnavigationEntity on Page {
  ...PageSlugEntity
  childPages {
    ...PageSlugEntity
  }
}

fragment AdminGroupEntity on AdminGroup {
  ...AdminGroupDocumentIdEntity
  title
  slug
  adminGroupId
  contentManagedBy
  landingPage {
    ...PageSubnavigationEntity
    localizations {
      ...PageSubnavigationEntity
    }
  }
}

query AdminGroups($limit: Int = -1) {
  adminGroups(pagination: { limit: $limit }) {
    ...AdminGroupEntity
  }
}
