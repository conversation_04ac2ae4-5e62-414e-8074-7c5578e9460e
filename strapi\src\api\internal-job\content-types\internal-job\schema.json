{"kind": "collectionType", "collectionName": "internal_jobs", "info": {"singularName": "internal-job", "pluralName": "internal-jobs", "displayName": "Internal Job"}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"jobType": {"type": "enumeration", "required": true, "enum": ["RECALCULATE_FULLPATH", "CREATE_REDIRECT"]}, "relatedDocumentId": {"type": "string", "required": false}, "payload": {"type": "json", "required": true}, "state": {"type": "enumeration", "enum": ["pending", "completed", "failed"], "required": true, "default": "pending"}, "error": {"type": "string"}}}