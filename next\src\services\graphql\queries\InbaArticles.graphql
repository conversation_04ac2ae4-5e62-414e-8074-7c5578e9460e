fragment InbaArticleSlugEntity on InbaArticle {
  documentId
  slug
  title
  locale
}

fragment InbaArticleCardEntity on InbaArticle {
  ...InbaArticleSlugEntity
  perex
  publishedAt
  coverImage {
    ...UploadImageSrcEntity
  }
  inbaTag {
    ...InbaTagEntity
  }
  tags {
    ...TagEntity
  }
}

fragment InbaArticleEntity on InbaArticle {
  ...InbaArticleCardEntity
  content
  inbaRelease {
    title
    releaseDate
    slug
  }
}

fragment InbaTagEntity on InbaTag {
  documentId
  title
}

query InbaArticleBySlug($slug: String!, $locale: I18NLocaleCode!) {
  inbaArticles(filters: { slug: { eq: $slug } }, locale: $locale) {
    ...InbaArticleEntity
  }
}

query InbaArticlesStaticPaths($locale: I18NLocaleCode!) {
  inbaArticles(locale: $locale, sort: "publishedAt:desc") {
    ...InbaArticleSlugEntity
  }
}

query InbaArticlesRssFeed($locale: I18NLocaleCode!) {
  inbaArticles(locale: $locale, sort: "publishedAt:desc") {
    documentId
    title
    slug
    perex
    publishedAt
    coverImage {
      url
    }
    inbaTag {
      documentId
      title
    }
    content
  }
}

query InbaTags($locale: I18NLocaleCode!) {
  inbaTags(locale: $locale) {
    ...InbaTagEntity
  }
}
