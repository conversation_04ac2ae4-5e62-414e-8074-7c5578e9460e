diff --git a/node_modules/strapi-plugin-meilisearch/dist/server/index.js b/node_modules/strapi-plugin-meilisearch/dist/server/index.js
index 20af84a..22487bc 100644
--- a/node_modules/strapi-plugin-meilisearch/dist/server/index.js
+++ b/node_modules/strapi-plugin-meilisearch/dist/server/index.js
@@ -808,22 +808,31 @@ const contentTypeService = ({ strapi: strapi2 }) => ({
     entriesQuery = {}
   }) {
     const batchSize = entriesQuery.limit || 500;
-    const entries_count = await this.numberOfEntries({
-      contentType: contentType2,
-      ...entriesQuery
-    });
-    const cbResponse = [];
-    for (let index2 = 0; index2 < entries_count; index2 += batchSize) {
+
+    // The problem is that `numberOfEntries()` does not return etries from all locales, but only the default locale, so the count of all entries is not correct.
+    // So we iterate over the entries using while loop, and break when there are no more entries.
+    // https://github.com/meilisearch/strapi-plugin-meilisearch/issues/1026#issuecomment-3105684670
+
+    const cbResponse = []
+    let index2 = 0
+
+    while (true) {
       const entries = await this.getEntries({
         start: index2,
         contentType: contentType2,
         ...entriesQuery
       }) || [];
-      if (entries.length > 0) {
-        const info = await callback({ entries, contentType: contentType2 });
-        if (Array.isArray(info)) cbResponse.push(...info);
-        else if (info) cbResponse.push(info);
+
+      // If no entries returned, we've reached the end
+      if (entries.length === 0) {
+        break
       }
+
+      const info = await callback({ entries, contentType: contentType2 });
+      if (Array.isArray(info)) cbResponse.push(...info);
+      else if (info) cbResponse.push(info);
+
+      index2 += batchSize
     }
     return cbResponse;
   }
@@ -1823,15 +1832,20 @@ const lifecycleService = ({ strapi: strapi2 }) => {
       });
       await strapi2.db.lifecycles.subscribe({
         models: [contentTypeUid],
+        // Change based on an unreleased fix to the plugin:
+        // https://github.com/meilisearch/strapi-plugin-meilisearch/commit/68c6c1de5f0f3e48435217afeb43a851109dfa70#diff-840292325e3f3afd470bce91820d878543a157635f4a67fd28c88fdf0616c757
+        // However, we don't receive populated relation fields in the event result
+        // Therefore we get this document using the Query Engine API
         async afterCreate(event) {
           const { result } = event;
           const meilisearch2 = strapi2.plugin("meilisearch").service("meilisearch");
-          const entry = await contentTypeService2.getEntry({
-            contentType: contentTypeUid,
-            documentId: result.documentId,
-            entriesQuery: meilisearch2.entriesQuery({ contentType: contentType2 })
-          });
-          meilisearch2.addEntriesToMeilisearch({
+          const entry = await strapi2.db.query(contentTypeUid).findOne({
+            where: {
+              id: result.id,
+            },
+            populate: true,
+          })
+          await meilisearch2.addEntriesToMeilisearch({
             contentType: contentTypeUid,
             entries: [entry]
           }).catch((e) => {
@@ -1869,17 +1883,14 @@ const lifecycleService = ({ strapi: strapi2 }) => {
             );
           });
         },
+        // Change based on an unreleased fix to the plugin:
+        // https://github.com/meilisearch/strapi-plugin-meilisearch/commit/68c6c1de5f0f3e48435217afeb43a851109dfa70#diff-840292325e3f3afd470bce91820d878543a157635f4a67fd28c88fdf0616c757
         async afterUpdate(event) {
           const { result } = event;
           const meilisearch2 = strapi2.plugin("meilisearch").service("meilisearch");
-          const entry = await contentTypeService2.getEntry({
+          await meilisearch2.updateEntriesInMeilisearch({
             contentType: contentTypeUid,
-            documentId: result.documentId,
-            entriesQuery: meilisearch2.entriesQuery({ contentType: contentType2 })
-          });
-          meilisearch2.updateEntriesInMeilisearch({
-            contentType: contentTypeUid,
-            entries: [entry]
+            entries: [result]
           }).catch((e) => {
             strapi2.log.error(
               `Meilisearch could not update entry with id: ${result.id}: ${e.message}`
