apiVersion: bitnami.com/v1alpha1
kind: SealedSecret
metadata:
  annotations:
    sealedsecrets.bitnami.com/namespace-wide: 'true'
  name: ${BUILD_REPOSITORY_NAME}-minio-secret
  namespace: ${NAMESPACE}
  labels:
    app: ${BUILD_REPOSITORY_NAME}
    source: ${BUILD_REPOSITORY_NAME}
spec:
  encryptedData:
    MINIO_ACCESS_KEY: AgAquCIiXSR2JoxsMregGZ39JToqLP9cUIe7F/WJ0R3/sUt0Kt8vNXkLVLXfrct4tp/EddLgmEUYiYGDoEyDiR4XUwVE/JujVGtjj5Oa+pN21n9F5aQZ1WNyPcmnlP+efiulGg/snC18Ue++ONSnUZIp6kl22+uqGJFtNKECVBKX2nw3J7BefFfvBsaGlXPLZspWtVfIjucEwITOjmhPwh2ePHjGrTgFniBZLYR6nyQLa6bGTBdHBivXVMfLLVW2BQlmCwXhANvwDdgm/gIdryzGy/XTHRVsRD2gxBl7L30ey9Z2yzM46YhVeJqkvGfRH9VzFr3PsZS35otD0f5Af2LH8HHQKPJNkUy02fuj122ZvDH93jrn+YKh44PtGznnmf+HmwuIYmJpR8yIyOeMd57UgjO/U2+nS3dSnSjLsde+gs0t9FO/g9g/ia31RFmW4L0Wb7A3F5Ml7FeY0IaHCrhsGUH9dvaFz64BaMhyrLxDT7/BzNiopgvMbRmWTN2h41AlVaYoW5JN4MvCJLPjvYGWuOx95yA6iZOnyqORmyUnP4xOIdIspdPaRsuO2BInMe6/ZikJiKUV8VaqlKR6aUtEpeorfGSjt1FPZ0CWnQl63M2I2cbkQpmPXlY2ioHTu9e2dw8B5h0yzmqwOK2WBK1DnV9rhLN64JG7XgvPhHBmLPtOt6/D5mFFE7cEt9DhS5eAOlzknzhwkLS2GTo=
    MINIO_SECRET_KEY: AgBtG+nmhvRQmui7PeWnZ20T01nnE/1t7RVaKXEuniKELy6cPpDuAzFSmVZBdRHRbCo+0ekSJNz8+Y95Ff7qIkWgFb6Atik49TBMQIT+5l5aCegt4x6oStllI+R0Qsiy2V0CaX+DkV7TLn4HajMcWZU7tUvYjdPw0qw4J8BGdSUoi8X+Na7f/FssWj6YrYVMWH4mKiFpcbPN4HmIu/dUMp23vX+YT6wc/ca9bto3vStzgkvaT2y32Fj8Y5zm7etdqALAFxRlIZMiu27fikFmZ655ibZHRjA/CCX7YiehaptBKNyy7Vv50gkhwkOmbYHyghP521yEDlD7QN1cT8TYDW5pzahhlI+QaIMvoFuGqHrkbdPSn4tXUNUfdBar7ODFkOcHa2xnQl1CYpXKHtkSVLpAN4rlDzsfcn1BAHvVpn82UNikCgYpvv7HwfKFiSixjvU+Ir8fUaObjkVCbS9hTOWhJCaacE3+K8imQqPvmZxsdJVKgoqcfuZn5pBbGizQC2kQDAaql14s32//9FVCP+T6+sq8Z3iNtMdVaMgBTqUvSMP/oCycwLGp+7qiVowzTwIoMev8Q7I0trZr1xhEmEy/MgLoMkJrMLsUJpSM1X/e4jE7vVfdSXy0zWAxzoMD3T+fbbgHZCOr9xK4NT1SOODKv9GwNd26m14x9ae6wDQCdmXFcnCXeeXBh69yrNRUH5rDAYsHIslfI1sOMIcrbQ4GblUUVzDsBkZKkS3Hw4rMuEp64G0zABGMqwmk4N8erxE3CH17GtfsEw==
  template:
    metadata:
      annotations:
        sealedsecrets.bitnami.com/namespace-wide: 'true'
      labels:
        app: ${BUILD_REPOSITORY_NAME}
        source: ${BUILD_REPOSITORY_NAME}
      name: ${BUILD_REPOSITORY_NAME}-minio-secret
      namespace: ${NAMESPACE}
